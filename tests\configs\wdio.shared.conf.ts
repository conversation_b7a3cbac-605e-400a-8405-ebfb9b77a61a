import { join } from 'path';
import type { Options } from '@wdio/types';
import allure from '@wdio/allure-reporter';
import * as fs from 'fs';
import * as path from 'path';
import { ExcelToJsonConverter } from '../../tests/support/utils/ExcelToJsonConverter.js';
import { RetryConfigTester } from '../support/utils/RetryConfigTester.js';
import {
  isHealthCheckOrServiceUrl,
  isServiceInitializationScenario,
  isSessionValid,
} from '../support/utils/HealthCheckDetector.js';

// Global variable to track retry attempts for proper SauceLabs reporting
const currentScenarioAttempts: Map<string, number> = new Map();
// Track final results to ensure correct SauceLabs status
const scenarioFinalResults: Map<string, { passed: boolean; attempts: number }> = new Map();
// Track all scenario results for final summary
const allScenarioResults: Array<{ name: string; feature: string; status: 'passed' | 'failed' | 'skipped'; attempts: number; tags?: string[] }> = [];
// Function to get the actual retry configuration from the current config
function getMaxRetries(): number {
  return RetryConfigTester.getMaxRetries();
}



export const config: Options.Testrunner = {
  //
  // ====================
  // Runner Configuration
  // ====================
  // WebdriverIO supports running e2e tests as well as unit and component tests.

  runner: 'local',
  autoCompileOpts: {
    autoCompile: true,
    tsNodeOpts: {
      project: './tsconfig.json',
      transpileOnly: true,
    },
  },
  //
  // =================
  // Service Providers
  // =================
  // WebdriverIO supports Sauce Labs, Browserstack, Testing Bot and LambdaTest (other cloud providers
  // should work too though). These services define specific user and key (or access key)
  // values you need to put in here in order to connect to these services.
  //
  //    =======================================================
  //    See wdio.saucelabs.shared.conf.ts for more information.
  //    =======================================================
  //
  // ==================
  // Specify Test Files
  // ==================
  // Define which test specs should run. The pattern is relative to the directory
  // of the configuration file being run.
  //
  // The specs are defined as an array of spec files (optionally using wildcards
  // that will be expanded). The test for each spec file will be run in a separate
  // worker process. In order to have a group of spec files run in the same worker
  // process simply enclose them in an array within the specs array.
  //
  // If you are calling `wdio` from an NPM script (see https://docs.npmjs.com/cli/run-script),
  // then the current working directory is where your `package.json` resides, so `wdio`
  // will be called from there.
  //

  // Patterns to exclude.
  exclude: [
    // 'path/to/excluded/files'
  ],
  //
  // ============
  // Capabilities
  // ============
  // Define your capabilities here. WebdriverIO can run multiple capabilities at the same
  // time. Depending on the number of capabilities, WebdriverIO launches several test
  // sessions. Within your capabilities you can overwrite the spec and exclude options in
  // order to group specific specs to a specific capability.
  //
  // First, you can define how many instances should be started at the same time. Let's
  // say you have 3 different capabilities (Chrome, Firefox, and Safari) and you have
  // set maxInstances to 1; wdio will spawn 3 processes. Therefore, if you have 10 spec
  // files and you set maxInstances to 10, all spec files will get tested at the same time
  // and 30 processes will get spawned. The property handles how many capabilities
  // from the same test should run tests.
  //
  maxInstances: 10,
  //
  // If you have trouble getting all important capabilities together, check out the
  // Sauce Labs platform configurator - a great tool to configure your capabilities:
  // https://saucelabs.com/platform/platform-configurator
  //
  //    =================================
  //    For capabilities see:
  //    - wdio.saucelabs.desktop.conf.ts
  //    =================================
  //
  capabilities: [],
  //
  // ===================
  // Test Configurations
  // ===================
  // Define all options that are relevant for the WebdriverIO instance here
  //
  // Level of logging verbosity: trace | debug | info | warn | error | silent
  logLevel: 'trace',
  //
  // Set specific log levels per logger
  // loggers:
  // - webdriver, webdriverio
  // - @wdio/browserstack-service, @wdio/devtools-service, @wdio/sauce-service
  // - @wdio/mocha-framework, @wdio/jasmine-framework
  // - @wdio/local-runner
  // - @wdio/sumologic-reporter
  // - @wdio/cli, @wdio/config, @wdio/utils
  // Level of logging verbosity: trace | debug | info | warn | error | silent
  // logLevels: {
  //     webdriver: 'info',
  //     '@wdio/appium-service': 'info'
  // },
  //
  // If you only want to run your tests until a specific amount of tests have failed use
  // bail (default is 0 - don't bail, run all tests).
  bail: 0,
  //
  // Set a base URL in order to shorten url command calls. If your `url` parameter starts
  // with `/`, the base url gets prepended, not including the path portion of your baseUrl.
  // If your `url` parameter starts without a scheme or `/` (like `some/path`), the base url
  // gets prepended directly.
  baseUrl: 'https://saucedemo.com',
  //
  // Default timeout for all waitFor* commands.
  waitforTimeout: 10000,
  //
  // Default timeout in milliseconds for request
  // if browser driver or grid doesn't send response
  connectionRetryTimeout: 120000,
  //
  // Default request retries count
  connectionRetryCount: 3,
  //
  // Test runner services
  // Services take over a specific job you don't want to take care of. They enhance
  // your test setup with almost no effort. Unlike plugins, they don't add new
  // commands. Instead, they hook themselves up into the test process.
  //
  //    ==============================================================
  //    For implementing Sauce Labs, see wdio.saucelabs.shared.conf.ts
  //    ==============================================================
  //
  services: [],

  // Framework you want to run your specs with.
  // The following are supported: Mocha, Jasmine, and Cucumber
  // see also: https://webdriver.io/docs/frameworks
  //
  // Make sure you have the wdio adapter package for the specific framework installed
  // before running any tests.
  framework: 'cucumber',
  //
  // The number of times to retry the entire specfile when it fails as a whole
  // specFileRetries: 1,
  //
  // Delay in seconds between the spec file retry attempts
  // specFileRetriesDelay: 0,
  //
  // Whether or not retried spec files should be retried immediately or deferred to the end of the queue
  // specFileRetriesDeferred: false,
  //
  // Test reporter for stdout.
  // The only one supported by default is 'dot'
  // see also: https://webdriver.io/docs/dot-reporter
  reporters: [
    'spec',
    ['allure', {
      outputDir: './reports/allure-results',
      disableWebdriverStepsReporting: true,  // Critical: prevents auto-step generation
      useCucumberStepReporter: false,        // Key: keeps false to avoid errors
      disableWebdriverScreenshotsReporting: false,
      disableMochaHooks: true,              // Prevents blank steps
      addConsoleLogs: false,
    }],
    // Custom reporter to show individual scenario names
    ['./tests/support/reporters/CustomCucumberReporter.ts', {}],
  ],

  // If you are using Cucumber you need to specify the location of your step definitions.
  cucumberOpts: {
    // <string[]> (file/dir) require files before executing features
    require: [join(process.cwd(), './tests/step-definitions/**/*.ts')],
    // <boolean> show full backtrace for errors
    backtrace: false,
    // <string[]> ("extension:module") require files with the given EXTENSION after requiring MODULE (repeatable)
    requireModule: [],
    // <boolean> invoke formatters without executing steps
    dryRun: false,
    // <boolean> abort the run on first failure
    failFast: false,
    // <boolean> hide step definition snippets for pending steps
    snippets: true,
    // <boolean> hide source uris
    source: true,
    // <boolean> fail if there are any undefined or pending steps
    strict: false,
    // <string> (expression) only execute the features or scenarios with tags matching the expression
    tagExpression: '',
    // <number> timeout for step definitions
    timeout: 60000,
    // <boolean> Enable this config to treat undefined definitions as warnings.
    ignoreUndefinedDefinitions: false,
  },
  //
  // =====
  // Hooks
  // =====
  // WebdriverIO provides several hooks you can use to interfere with the test process in order to enhance
  // it and to build services around it. You can either apply a single function or an array of
  // methods to it. If one of them returns with a promise, WebdriverIO will wait until that promise got
  // resolved to continue.
  /**
   * Gets executed once before all workers get launched.
   * Performs one-time setup including Excel to JSON conversion
   */
  onPrepare: async function () {
    console.log('🚀 Preparing test environment...');

    // Perform Excel to JSON conversion once at the start
    const excelFilePath = path.join(process.cwd(), 'data', 'aem-mobile-camel.xlsx');
    const jsonFilePath = path.join(process.cwd(), 'data', 'aem-mobile-camel.json');

    if (fs.existsSync(excelFilePath)) {
      // Only convert if JSON doesn't exist or Excel is newer than JSON
      let shouldConvert = !fs.existsSync(jsonFilePath);

      if (!shouldConvert) {
        const excelStats = fs.statSync(excelFilePath);
        const jsonStats = fs.statSync(jsonFilePath);
        shouldConvert = excelStats.mtime > jsonStats.mtime;
      }

      if (shouldConvert) {
        console.log('📊 Converting Excel to JSON (one-time setup)...');
        const converter = new ExcelToJsonConverter(excelFilePath);
        await converter.convertToJson();
        console.log('✅ Excel test data conversion complete.');
      } else {
        console.log('✅ Using existing JSON file (up to date).');
      }

      console.log('✅ Excel to JSON conversion completed successfully.');
    } else {
      console.warn(`⚠️  Excel file not found at ${excelFilePath}. Using existing JSON if available.`);
    }
  },

  beforeScenario: async function (world) {
    const scenarioName = world.pickle.name;
    const featureName = world.pickle.uri?.split('/').pop()?.replace('.feature', '') || 'Unknown Feature';

    // Filter out health checks and service initialization scenarios from SauceLabs reporting
    if (isServiceInitializationScenario(scenarioName, featureName)) {
      console.log(`🔧 Skipping SauceLabs reporting for service initialization: ${scenarioName}`);
      return; // Skip SauceLabs reporting for health checks and service initialization
    }

    // Check if this is a health check URL scenario
    try {
      const currentUrl = await browser.getUrl();
      if (isHealthCheckOrServiceUrl(currentUrl)) {
        console.log(`🔧 Skipping SauceLabs reporting for health check URL: ${currentUrl}`);
        return; // Skip SauceLabs reporting for health check URLs
      }
    } catch {
      // If we can't get URL, continue with normal processing
      console.log('⚠️  Could not check URL for health check filtering, proceeding normally');
    }

    // Log retry configuration for debugging (only once per test run)
    if (!currentScenarioAttempts.has('_config_logged')) {
      RetryConfigTester.logRetryConfig();
      RetryConfigTester.validateRetryConfig();
      currentScenarioAttempts.set('_config_logged', 1);
    }

    // Track retry attempts for this scenario
    const currentAttempt = (currentScenarioAttempts.get(scenarioName) || 0) + 1;
    currentScenarioAttempts.set(scenarioName, currentAttempt);

    // Force new session for EVERY scenario (not just retries) to ensure proper isolation
    console.log(`🆕 Starting attempt ${currentAttempt} with FRESH SESSION for: ${scenarioName}`);

    if (currentAttempt > 1) {
      console.log('🔄 Previous attempt(s) failed, creating new session and device allocation');
    } else {
      console.log('🔄 Creating new session for scenario isolation');
    }

    // Force creation of a completely new session for every scenario
    try {
      console.log('🔄 Forcing new session creation...');
      await browser.reloadSession();
      console.log('✅ New session created successfully');

      // Give extra time for new session initialization
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (reloadError) {
      console.log('⚠️  Session reload failed:', reloadError instanceof Error ? reloadError.message : String(reloadError));
      console.log('🔄 Continuing with existing session...');
    }

    // Allow SauceLabs to complete health checks and session initialization
    console.log('⏳ Waiting for SauceLabs session to be fully ready...');
    await new Promise(resolve => setTimeout(resolve, 3000)); // Give SauceLabs extra time for fresh sessions

    // Verify we have a valid session and set up keep-alive
    try {
      const sessionId = browser.sessionId;
      console.log(`✅ Session ready: ${sessionId} for ${scenarioName} (Attempt ${currentAttempt})`);

      // Get device and platform information to verify fresh allocation
      const capabilities = browser.capabilities as Record<string, unknown>;
      const deviceName = (capabilities['appium:deviceName'] as string) ||
        (capabilities.deviceName as string) ||
        'Unknown Device';
      const platformVersion = (capabilities['appium:platformVersion'] as string) ||
        (capabilities.platformVersion as string) ||
        'Unknown Version';

      console.log(`📱 Device allocated: ${deviceName} (${platformVersion}) for ${scenarioName} attempt ${currentAttempt}`);

      // Send a keep-alive command to prevent session timeout
      await browser.execute('sauce:context=Session initialized and ready for test execution');
      console.log('🔄 Session keep-alive sent');
    } catch (sessionError) {
      console.log('⚠️  Session verification failed:', sessionError instanceof Error ? sessionError.message : String(sessionError));
    }

    try {
      if (await isSessionValid()) {
        console.log('✅ Session is ready, proceeding with test...');
      } else {
        console.log('⚠️  Session validation failed, but proceeding...');
      }
    } catch (error) {
      console.log('Session validation error, proceeding anyway:', error instanceof Error ? error.message : String(error));
    }

    // Set clean test name for SauceLabs reporting with session isolation info
    const retryInfo = currentAttempt > 1 ? ` - Retry ${currentAttempt}` : '';
    const sessionInfo = ` [Session: ${browser.sessionId.substring(0, 8)}]`;
    const testName = `${scenarioName}${retryInfo}${sessionInfo}`;
    await browser.execute('sauce:job-name=' + testName);

    // Add comprehensive context for scenario-level session reporting
    await browser.execute('sauce:context=Feature: ' + featureName);
    await browser.execute('sauce:context=Scenario: ' + scenarioName);
    await browser.execute('sauce:context=Session Type: Scenario-Level Isolation');
    await browser.execute('sauce:context=Attempt: ' + currentAttempt);

    // Add device allocation information
    const capabilities = browser.capabilities as Record<string, unknown>;
    const deviceName = (capabilities['appium:deviceName'] as string) ||
      (capabilities.deviceName as string) ||
      'Unknown Device';
    const platformVersion = (capabilities['appium:platformVersion'] as string) ||
      (capabilities.platformVersion as string) ||
      'Unknown Version';
    await browser.execute('sauce:context=Device: ' + deviceName + ' (' + platformVersion + ')');

    // Add tags as context if available
    if (world.pickle.tags && world.pickle.tags.length > 0) {
      const tags = world.pickle.tags.map(tag => tag.name).join(', ');
      await browser.execute('sauce:context=Tags: ' + tags);
    }

    // Mark test as in progress
    await browser.execute('sauce:context=Status: Running');
    allure.addLabel('feature', world.pickle.name);

    // Use already declared capabilities, deviceName, and platformVersion variables
    allure.addArgument('Device', deviceName);

    // Extract platform information
    const platformName = (capabilities.platformName as string) || 'Unknown Platform';
    allure.addArgument('Platform', `${platformName} ${platformVersion}`.trim());

    // Extract browser information
    const browserName = (capabilities.browserName as string) || 'Unknown Browser';
    const browserVersion = (capabilities.browserVersion as string) || '';
    allure.addArgument('Browser', `${browserName} ${browserVersion}`.trim());

    // Add additional environment information if available
    if (capabilities['sauce:options']) {
      const sauceOptions = capabilities['sauce:options'] as Record<string, unknown>;
      if (sauceOptions.build) {
        allure.addArgument('Build', sauceOptions.build as string);
      }
    }
  },
  /**
   * Function to be executed after a test (in Mocha/Jasmine only)
   * @param {object}  test             test object
   * @param {object}  _context         scope object the test was executed with (unused)
   * @param {Error}   result.error     error object in case the test fails, otherwise `undefined`
   * @param {*}       result.result    return object of test function
   * @param {number}  result.duration  duration of test
   * @param {boolean} result.passed    true if test has passed, otherwise false
   * @param {object}  result.retries   informations to spec related retries, e.g. `{ attempts: 0, limit: 0 }`
   */
  afterTest: async function (test, _context, { error, result, duration, passed, retries }) {
    // Add test status to Allure report
    allure.addLabel('status', passed ? 'passed' : 'failed');

    // Add test duration information
    allure.addArgument('duration', `${duration}ms`);

    // Add retry information
    allure.addArgument('retries', `${retries.attempts} of ${retries.limit}`);

    // Add test result information if available
    if (result) {
      allure.addArgument('result', typeof result === 'object' ? JSON.stringify(result) : String(result));
    }

    // Handle failed tests - capture screenshots and error messages
    if (!passed) {
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, '');
      const screenshot = await browser.takeScreenshot();
      const screenshotPath = `./reports/screenshots/failure-${test.parent}-${test.title}-${timestamp}.png`;

      // Ensure the directory exists
      fs.mkdirSync(path.dirname(screenshotPath), { recursive: true });

      // Save screenshot to file
      fs.writeFileSync(screenshotPath, Buffer.from(screenshot, 'base64'));

      // Attach screenshot to Allure report
      allure.addAttachment(
        `Failure-Screenshot-${test.title}`,
        Buffer.from(screenshot, 'base64').toString('base64'),
        'image/png',
      );

      // Also attach error message if there is one
      if (error) {
        allure.addAttachment(
          'Error Message',
          error.message,
          'text/plain',
        );
      }
    }
  },
  /**
   * Hook that runs before the test session starts
   * Excel conversion is now handled in onPrepare hook
   */
  beforeSession: async function () {
    console.log('📋 Setting up worker session for test execution...');

    // Verify JSON file exists (should be created by onPrepare hook)
    const jsonFilePath = path.join(process.cwd(), 'data', 'aem-mobile-camel.json');
    if (fs.existsSync(jsonFilePath)) {
      console.log('✅ Test data JSON file found. Worker ready for test execution.');
    } else {
      console.warn('⚠️  Test data JSON file not found. Tests may fail if they require test data.');
    }
  },
  afterScenario: async function (world, result) {
    console.log(`Cleaning up after: ${world.pickle.name}`);
    const scenarioName = world.pickle.name;
    const featureName = world.pickle.uri?.split('/').pop()?.replace('.feature', '') || 'Unknown Feature';

    // Filter out health checks and service initialization scenarios from SauceLabs reporting
    if (isServiceInitializationScenario(scenarioName, featureName)) {
      console.log(`🔧 Skipping SauceLabs cleanup for service initialization: ${scenarioName}`);
      return; // Skip SauceLabs cleanup for health checks and service initialization
    }

    // Check if this is a health check URL scenario
    try {
      const currentUrl = await browser.getUrl();
      if (isHealthCheckOrServiceUrl(currentUrl)) {
        console.log(`🔧 Skipping SauceLabs cleanup for health check URL: ${currentUrl}`);
        return; // Skip SauceLabs cleanup for health check URLs
      }
    } catch {
      // If we can't get URL, continue with normal processing
      console.log('⚠️  Could not check URL for health check cleanup filtering, proceeding normally');
    }

    // Enhanced logging for scenario results
    const statusIcon = result.passed ? '✅' : '❌';
    const currentAttempt = currentScenarioAttempts.get(scenarioName) || 1;
    console.log(`${statusIcon} SCENARIO RESULT: "${scenarioName}" in "${featureName}" - ${result.passed ? 'PASSED' : 'FAILED'} (Attempt ${currentAttempt})`);

    // Add tags information if available
    if (world.pickle.tags && world.pickle.tags.length > 0) {
      const tags = world.pickle.tags.map(tag => tag.name).join(', ');
      console.log(`   📋 Tags: ${tags}`);
    }

    try {
      const currentAttempt = currentScenarioAttempts.get(scenarioName) || 1;
      const featureName = world.pickle.uri?.split('/').pop()?.replace('.feature', '') || 'Unknown Feature';

      // Store the result for this attempt
      scenarioFinalResults.set(scenarioName, {
        passed: result.passed,
        attempts: currentAttempt,
      });

      // Test name will be updated in the specific pass/fail handling below

      if (result.passed) {
        console.log(`✅ Scenario PASSED: ${scenarioName} (Attempt ${currentAttempt})`);

        // Track scenario result for final summary
        allScenarioResults.push({
          name: scenarioName,
          feature: featureName,
          status: 'passed',
          attempts: currentAttempt,
          tags: world.pickle.tags?.map(tag => tag.name),
        });

        // Update test name to show final PASSED status
        const finalTestName = `${scenarioName} - PASSED`;
        await browser.execute('sauce:job-name=' + finalTestName);

        // For passed scenarios, immediately set the final status
        await browser.execute('sauce:job-result=passed');
        await browser.execute('sauce:context=Final Result: PASSED after ' + currentAttempt + ' attempt(s)');

        // Double-confirm the passed status with additional context
        await browser.execute('sauce:job-result=passed');
        await browser.execute('sauce:context=Test completed successfully');

        // Clear retry tracking for passed scenarios but keep final result for status override
        currentScenarioAttempts.delete(scenarioName);
        // Keep scenarioFinalResults for final status override in after hook

        console.log('✅ Scenario passed - session will be cleaned up naturally by WebDriverIO');

      } else {
        console.log(`❌ Scenario FAILED: ${scenarioName} (Attempt ${currentAttempt})`);

        // For failed scenarios, we need to determine if this is the final attempt
        await browser.execute('sauce:context=Attempt ' + currentAttempt + ': FAILED');

        // Add failure reason if available
        if (result.error) {
          const errorMessage = typeof result.error === 'string'
            ? result.error
            : (result.error as Error).message || String(result.error);
          await browser.execute('sauce:context=Error: ' + errorMessage.substring(0, 200));
        }

        // Check if this scenario will be retried by looking at the retry configuration
        // If this is the final attempt, set the final failed status
        const maxRetries = getMaxRetries();
        console.log(`📊 Retry info: Current attempt ${currentAttempt} of max ${maxRetries} for ${scenarioName}`);

        if (currentAttempt >= maxRetries) {
          console.log(`🔴 Final attempt reached for ${scenarioName}. Setting final FAILED status.`);

          // Track scenario result for final summary
          allScenarioResults.push({
            name: scenarioName,
            feature: featureName,
            status: 'failed',
            attempts: currentAttempt,
            tags: world.pickle.tags?.map(tag => tag.name),
          });

          // Update test name to show final FAILED status
          const finalTestName = `${scenarioName} - FAILED`;
          await browser.execute('sauce:job-name=' + finalTestName);

          await browser.execute('sauce:job-result=failed');
          await browser.execute('sauce:context=Final Result: FAILED after ' + currentAttempt + ' attempt(s)');

          // Clean up tracking for this scenario but keep final result for status override
          currentScenarioAttempts.delete(scenarioName);
          // Keep scenarioFinalResults for final status override in after hook
        } else {
          console.log(`🔄 Scenario ${scenarioName} will be retried. Not setting final status yet.`);
          // For non-final attempts, don't set job-result to avoid premature status setting
          await browser.execute('sauce:context=Will retry - attempt ' + currentAttempt + ' of ' + maxRetries);

          // Update test name to show retry status
          const retryTestName = `${scenarioName} - Retrying (${currentAttempt}/${maxRetries})`;
          await browser.execute('sauce:job-name=' + retryTestName);
        }
      }

      // Determine session cleanup strategy
      const maxRetries = getMaxRetries();
      const isFinalAttempt = currentAttempt >= maxRetries;
      const shouldTerminateSession = result.passed || isFinalAttempt;

      if (shouldTerminateSession) {
        console.log(`🔄 Final cleanup for ${scenarioName} (Final: ${isFinalAttempt ? 'Yes' : 'No'}, Passed: ${result.passed ? 'Yes' : 'No'})`);

        // Quick session validation with timeout
        let sessionValid = false;
        try {
          // Use a quick command with short timeout to check session
          await Promise.race([
            browser.getUrl(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Session check timeout')), 5000)),
          ]);
          sessionValid = true;
          console.log('✅ Session is active for final cleanup');
        } catch {
          console.log('⚠️  Session appears inactive, skipping cleanup operations');
        }

        // Skip cookie cleanup for passed scenarios to avoid delays
        if (sessionValid && !result.passed) {
          try {
            console.log('🔄 Performing cookie cleanup for failed scenario');
            await Promise.race([
              browser.deleteAllCookies(),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Cookie cleanup timeout')), 10000)),
            ]);
            console.log(`✅ Cookie cleanup completed for ${scenarioName}`);
          } catch (cleanupError) {
            console.log(`⚠️  Cookie cleanup skipped due to timeout or error: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`);
          }
        } else if (result.passed) {
          console.log('✅ Scenario passed - session will be cleaned up naturally by WebDriverIO');
          // Let WebDriverIO handle session termination naturally to avoid conflicts
          // The session isolation is achieved through maxInstances: 1 configuration
        }

        // Let WebDriverIO handle session termination naturally for passed tests
        if (!result.passed && sessionValid) {
          try {
            console.log(`🔄 Terminating session for failed scenario: ${scenarioName}`);
            await browser.deleteSession();
            console.log(`✅ Session terminated successfully for ${scenarioName}`);
          } catch (sessionTerminationError) {
            console.log(`⚠️  Session termination failed (session may have auto-terminated): ${sessionTerminationError instanceof Error ? sessionTerminationError.message : String(sessionTerminationError)}`);
          }
        }
      } else {
        console.log(`🔄 Keeping session alive for retry. Current attempt: ${currentAttempt}/${maxRetries}`);
      }

    } catch (error) {
      console.log(`Session cleanup failed for ${scenarioName}:`, error instanceof Error ? error.message : String(error));
      // Don't throw error here as it might interfere with retry mechanism
    }
  },



  /**
   * Hook that gets executed before test execution begins
   * Configure element highlighting based on environment variable
   */
  before: function () {
    // Set up element highlighting configuration
    browser.highlightElements = process.env.HIGHLIGHT_ELEMENTS === 'true';

    // Configure highlight appearance
    if (browser.highlightElements) {
      console.log('Element highlighting is ENABLED');
    } else {
      console.log('Element highlighting is DISABLED - using scrollIntoView as fallback');
    }
  },

  /**
   * Hook that gets executed after all tests are completed
   * This runs after the SauceLabs service has set its automatic status
   * We use this to override with our manually controlled status
   */
  // after: async function () {
  //   // Override any automatic SauceLabs status setting with our final manual status
  //   // This ensures our manually set status in afterScenario takes precedence
  //   try {
  //     // Check if we have any final results that need to be preserved
  //     for (const [scenarioName, result] of scenarioFinalResults.entries()) {
  //       console.log(`🔧 Final status override for ${scenarioName}: ${result.passed ? 'PASSED' : 'FAILED'}`);

  //       if (result.passed) {
  //         await browser.execute('sauce:job-result=passed');
  //         await browser.execute('sauce:context=Final Override: PASSED - Manual Status Control');
  //       } else {
  //         await browser.execute('sauce:job-result=failed');
  //         await browser.execute('sauce:context=Final Override: FAILED - Manual Status Control');
  //       }
  //     }

  //     // Clear the final results after override
  //     scenarioFinalResults.clear();
  //   } catch (error) {
  //     console.log('⚠️  Could not override final SauceLabs status:', error instanceof Error ? error.message : String(error));
  //   }
  // },

  /**
   * Hook that gets executed after all tests are completed
   * Clean up retry tracking and handle final failed scenarios
   */
  onComplete: async function () {
    console.log('Test execution completed. Processing final results...');

    // Handle any scenarios that failed after all retries
    for (const [scenarioName, result] of scenarioFinalResults.entries()) {
      if (!result.passed) {
        console.log(`🔴 Final status: ${scenarioName} FAILED after ${result.attempts} attempts`);
        try {
          // Set final failed status for scenarios that exhausted all retries
          await browser.execute('sauce:job-result=failed');
          await browser.execute('sauce:context=Final Result: FAILED after ' + result.attempts + ' attempt(s) - No more retries');
        } catch (error) {
          console.log(`Could not update final status for ${scenarioName}:`, error);
        }
      }
    }

    // Display final scenario summary
    if (allScenarioResults.length > 0) {
      console.log('\n' + '='.repeat(100));
      console.log('📊 SCENARIO EXECUTION SUMMARY');
      console.log('='.repeat(100));

      const passedScenarios = allScenarioResults.filter(s => s.status === 'passed');
      const failedScenarios = allScenarioResults.filter(s => s.status === 'failed');
      const skippedScenarios = allScenarioResults.filter(s => s.status === 'skipped');

      console.log(`\n📈 TOTALS: ${allScenarioResults.length} scenarios | ${passedScenarios.length} passed | ${failedScenarios.length} failed | ${skippedScenarios.length} skipped\n`);

      // Group by feature
      const byFeature = allScenarioResults.reduce((acc, scenario) => {
        if (!acc[scenario.feature]) acc[scenario.feature] = [];
        acc[scenario.feature].push(scenario);
        return acc;
      }, {} as Record<string, typeof allScenarioResults>);

      for (const [feature, scenarios] of Object.entries(byFeature)) {
        console.log(`📁 ${feature}:`);
        scenarios.forEach(scenario => {
          const icon = scenario.status === 'passed' ? '✅' : scenario.status === 'failed' ? '❌' : '⏭️';
          const retryInfo = scenario.attempts > 1 ? ` (${scenario.attempts} attempts)` : '';
          const tagsInfo = scenario.tags && scenario.tags.length > 0 ? ` [${scenario.tags.join(', ')}]` : '';
          console.log(`   ${icon} ${scenario.name}${retryInfo}${tagsInfo}`);
        });
        console.log('');
      }

      console.log('='.repeat(100));
    }

    // Clean up tracking maps
    currentScenarioAttempts.clear();
    scenarioFinalResults.clear();
    allScenarioResults.length = 0; // Clear the array
  },


};

/**
 * Hook that gets executed after the suite has ended
 * @param {object} suite suite details
 */
// afterSuite: function (suite) {
// },
/**
 * Runs after a WebdriverIO command gets executed
 * @param {string} commandName hook command name
 * @param {Array} args arguments that command would receive
 * @param {number} result 0 - command success, 1 - command error
 * @param {object} error error object if any
 */
// afterCommand: function (commandName, args, result, error) {
// },
/**
 * Gets executed after all tests are done. You still have access to all global variables from
 * the test.
 * @param {number} result 0 - test pass, 1 - test fail
 * @param {Array.<Object>} capabilities list of capabilities details
 * @param {Array.<String>} specs List of spec file paths that ran
 */
// after: function (result, capabilities, specs) {
// },
/**
 * Gets executed right after terminating the webdriver session.
 * @param {object} config wdio configuration object
 * @param {Array.<Object>} capabilities list of capabilities details
 * @param {Array.<String>} specs List of spec file paths that ran
 */
// afterSession: function (config, capabilities, specs) {
// },
/**
 * Gets executed after all workers got shut down and the process is about to exit. An error
 * thrown in the onComplete hook will result in the test run failing.
 * @param {object} exitCode 0 - success, 1 - fail
 * @param {object} config wdio configuration object
 * @param {Array.<Object>} capabilities list of capabilities details
 * @param {<Object>} results object containing test results
 */
// onComplete: function(exitCode, config, capabilities, results) {
// },
/**
 * Gets executed when a refresh happens.
 * @param {string} oldSessionId session ID of the old session
 * @param {string} newSessionId session ID of the new session
 */
// onReload: function(oldSessionId, newSessionId) {
// }

