#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Run Sensa tests for different environments
.DESCRIPTION
    This script allows you to easily run Sensa tests for QA or Production environments
.PARAMETER Environment
    The environment to run tests for (QA or PROD). Default is QA.
.PARAMETER Tags
    Optional: Specific tags to run (will override environment-based tag selection)
.PARAMETER Retry
    Optional: Number of retries (will override config-based retry settings)
.EXAMPLE
    .\run-sensa-tests.ps1 -Environment QA
.EXAMPLE
    .\run-sensa-tests.ps1 -Environment PROD
.EXAMPLE
    .\run-sensa-tests.ps1 -Environment QA -Tags "@SensaLoginPage_Validation_QA"
.EXAMPLE
    .\run-sensa-tests.ps1 -Environment QA -Retry 1
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("QA", "PROD")]
    [string]$Environment = "QA",
    
    [Parameter(Mandatory=$false)]
    [string]$Tags = "",
    
    [Parameter(Mandatory=$false)]
    [int]$Retry = -1
)

# Set environment variable
$env:TEST_ENV = $Environment

# Build the command
$command = "wdio run ./tests/configs/wdio.saucelabs.mobile-sensa.conf.ts"

# Add tags if specified
if ($Tags -ne "") {
    $command += " --cucumberOpts.tagExpression='$Tags'"
}

# Add retry if specified
if ($Retry -ge 0) {
    $command += " --retry $Retry"
}

# Display what we're running
Write-Host "🚀 Running Sensa tests..." -ForegroundColor Green
Write-Host "   Environment: $Environment" -ForegroundColor Cyan
if ($Tags -ne "") {
    Write-Host "   Tags: $Tags" -ForegroundColor Cyan
}
if ($Retry -ge 0) {
    Write-Host "   Retry: $Retry" -ForegroundColor Cyan
}
Write-Host "   Command: $command" -ForegroundColor Yellow
Write-Host ""

# Execute the command
try {
    Invoke-Expression $command
    Write-Host "✅ Test execution completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Test execution failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
